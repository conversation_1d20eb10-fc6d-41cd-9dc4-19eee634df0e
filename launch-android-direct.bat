@echo off
echo ========================================
echo   LANCEMENT DIRECT DE L'EMULATEUR
========================================
echo.

set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
set "EMULATOR_PATH=%ANDROID_HOME%\emulator\emulator.exe"

echo 🚀 Lancement de l'émulateur Android...
echo.

start "" "%EMULATOR_PATH%" -avd Medium_Phone_API_36.0

echo ✅ Émulateur en cours de démarrage...
echo.
echo 📋 Prochaines étapes :
echo 1. Attendez que l'émulateur soit complètement démarré
echo 2. Dans VS Code, ouvrez un terminal (Ctrl + `)
echo 3. Naviguez vers react-native : cd react-native
echo 4. Lancez l'application : npx expo start
echo 5. Appuyez sur 'a' pour Android
echo.
echo 🔑 Compte de test pour votre application :
echo Email: <EMAIL>
echo Mot de passe: Tech123
echo.

pause
