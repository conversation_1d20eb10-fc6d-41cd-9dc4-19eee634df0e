import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Linking,
  SafeAreaView
} from 'react-native';
import { Picker } from '@react-native-picker/picker';

const API_BASE_URL = 'http://***********:4000'; // IP de votre PC + port backend

const HistoriquePageRN = ({ navigation }) => {
  const [clients, setClients] = useState([]);
  const [filteredClients, setFilteredClients] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sectorFilter, setSectorFilter] = useState('all');
  const [secteurs, setSecteurs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadClients();
    loadSecteurs();
  }, []);

  useEffect(() => {
    filterClients();
  }, [clients, searchTerm, sectorFilter]);

  const loadClients = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('📥 Chargement des clients depuis la base de données...');
      
      const response = await fetch(`${API_BASE_URL}/api/clients`);
      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ ${data.data.length} clients récupérés`);
        setClients(data.data);
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des clients');
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des clients:', error);
      setError('Erreur lors du chargement des clients: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const loadSecteurs = async () => {
    try {
      console.log('📥 Chargement des secteurs...');
      const response = await fetch(`${API_BASE_URL}/api/secteurs`);
      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ ${data.data.length} secteurs récupérés`);
        setSecteurs(data.data);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des secteurs:', error);
    }
  };

  const filterClients = () => {
    let filtered = clients;

    // Filtrage par terme de recherche
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(client => 
        client.nom.toLowerCase().includes(term) ||
        client.prenom.toLowerCase().includes(term) ||
        client.ville.toLowerCase().includes(term) ||
        client.email.toLowerCase().includes(term) ||
        client.tel.includes(term)
      );
    }

    // Filtrage par secteur
    if (sectorFilter !== 'all') {
      filtered = filtered.filter(client => client.ids === parseInt(sectorFilter));
    }

    setFilteredClients(filtered);
  };

  const handleLocalisation = async (client) => {
    try {
      console.log(`🗺️ Localisation demandée pour le client: ${client.nom} ${client.prenom}`);
      
      // Construire l'URL de recherche Google Maps avec l'adresse du client
      const searchQuery = encodeURIComponent(`${client.adresse}, ${client.ville || ''}`);
      const googleMapsUrl = `https://www.google.com/maps/search/${searchQuery}`;
      
      // Ouvrir Google Maps
      const supported = await Linking.canOpenURL(googleMapsUrl);
      if (supported) {
        await Linking.openURL(googleMapsUrl);
        console.log(`✅ Google Maps ouvert pour: ${client.adresse}, ${client.ville}`);
      } else {
        Alert.alert('Erreur', 'Impossible d\'ouvrir Google Maps');
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'ouverture de Google Maps:', error);
      Alert.alert('Erreur', 'Erreur lors de l\'ouverture de Google Maps');
    }
  };

  const getSecteurName = (secteurId) => {
    const secteur = secteurs.find(s => s.ids === secteurId);
    return secteur ? secteur.nom : 'Non défini';
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>← Retour</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Liste des Clients</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <Text style={styles.loadingText}>Chargement des clients...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>← Retour</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Liste des Clients</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorIcon}>❌</Text>
          <Text style={styles.errorTitle}>Erreur de chargement</Text>
          <Text style={styles.errorMessage}>{error}</Text>
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={loadClients}
          >
            <Text style={styles.retryButtonText}>🔄 Réessayer</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>← Retour</Text>
        </TouchableOpacity>
        <View>
          <Text style={styles.title}>Liste des Clients</Text>
          <Text style={styles.subtitle}>
            {filteredClients.length} client(s) • Base de données Facutration
          </Text>
        </View>
      </View>

      {/* Barre de recherche et filtres */}
      <View style={styles.searchContainer}>
        <Text style={styles.sectionTitle}>🔍 Recherche et Filtres</Text>
        <TouchableOpacity 
          style={styles.refreshButton}
          onPress={loadClients}
        >
          <Text style={styles.refreshButtonText}>🔄 Actualiser</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.filtersContainer}>
        {/* Barre de recherche */}
        <TextInput
          style={styles.searchInput}
          placeholder="🔍 Rechercher par nom, prénom, ville, email ou téléphone..."
          value={searchTerm}
          onChangeText={setSearchTerm}
        />

        {/* Filtre par secteur */}
        <View style={styles.pickerContainer}>
          <Picker
            selectedValue={sectorFilter}
            onValueChange={setSectorFilter}
            style={styles.picker}
          >
            <Picker.Item 
              label={`🏘️ Tous les secteurs (${clients.length})`} 
              value="all" 
            />
            {secteurs.map(secteur => (
              <Picker.Item 
                key={secteur.ids} 
                label={`📍 ${secteur.nom} (${clients.filter(c => c.ids === secteur.ids).length})`}
                value={secteur.ids.toString()} 
              />
            ))}
          </Picker>
        </View>
      </View>

      {/* Liste des clients */}
      <ScrollView style={styles.clientsList}>
        {filteredClients.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>👥</Text>
            <Text style={styles.emptyTitle}>Aucun client trouvé</Text>
            <Text style={styles.emptyMessage}>
              {searchTerm || sectorFilter !== 'all' 
                ? 'Aucun client ne correspond aux critères de recherche.' 
                : 'Aucun client n\'est enregistré dans la base de données.'
              }
            </Text>
            {(searchTerm || sectorFilter !== 'all') && (
              <TouchableOpacity
                style={styles.resetButton}
                onPress={() => {
                  setSearchTerm('');
                  setSectorFilter('all');
                }}
              >
                <Text style={styles.resetButtonText}>🔄 Réinitialiser les filtres</Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          filteredClients.map(client => (
            <View key={client.idclient} style={styles.clientCard}>
              <View style={styles.clientHeader}>
                <View style={styles.clientInfo}>
                  <View style={styles.clientNameRow}>
                    <Text style={styles.clientIcon}>👤</Text>
                    <Text style={styles.clientName}>{client.nom} {client.prenom}</Text>
                  </View>
                  <Text style={styles.clientAddress}>
                    📍 {client.adresse}
                    {client.ville && `, ${client.ville}`}
                  </Text>
                  <Text style={styles.clientSector}>
                    🏘️ Secteur: {getSecteurName(client.ids)}
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.locateButton}
                  onPress={() => handleLocalisation(client)}
                >
                  <Text style={styles.locateButtonText}>🗺️ Localiser</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.clientDetails}>
                {client.tel && (
                  <Text style={styles.clientContact}>📞 {client.tel}</Text>
                )}
                {client.email && (
                  <Text style={styles.clientContact}>📧 {client.email}</Text>
                )}
              </View>
            </View>
          ))
        )}
      </ScrollView>

      {/* Statistiques des clients */}
      {clients.length > 0 && (
        <View style={styles.statsContainer}>
          <Text style={styles.statsTitle}>📊 Statistiques des Clients</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statIcon}>👥</Text>
              <Text style={styles.statNumber}>{clients.length}</Text>
              <Text style={styles.statLabel}>Total Clients</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statIcon}>🔍</Text>
              <Text style={styles.statNumber}>{filteredClients.length}</Text>
              <Text style={styles.statLabel}>Affichés</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statIcon}>🏘️</Text>
              <Text style={styles.statNumber}>{secteurs.length}</Text>
              <Text style={styles.statLabel}>Secteurs</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statIcon}>📧</Text>
              <Text style={styles.statNumber}>
                {clients.filter(c => c.email && c.email.trim()).length}
              </Text>
              <Text style={styles.statLabel}>Avec Email</Text>
            </View>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    marginRight: 16,
    padding: 8,
    backgroundColor: '#3b82f6',
    borderRadius: 6,
  },
  backButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#dc2626',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  refreshButton: {
    backgroundColor: '#10b981',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  refreshButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  filtersContainer: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 8,
  },
  searchInput: {
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 15,
    backgroundColor: 'white',
  },
  pickerContainer: {
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    backgroundColor: 'white',
  },
  picker: {
    height: 50,
  },
  clientsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 40,
    backgroundColor: 'white',
    borderRadius: 8,
    marginVertical: 20,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 20,
  },
  resetButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  resetButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  clientCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  clientInfo: {
    flex: 1,
  },
  clientNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  clientIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  clientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  clientAddress: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  clientSector: {
    fontSize: 12,
    color: '#9ca3af',
  },
  locateButton: {
    backgroundColor: '#10b981',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 4,
  },
  locateButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  clientDetails: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  clientContact: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 4,
  },
  statsContainer: {
    backgroundColor: 'white',
    padding: 16,
    margin: 16,
    borderRadius: 8,
  },
  statsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    backgroundColor: '#f8fafc',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 8,
  },
  statIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
});

export default HistoriquePageRN;
