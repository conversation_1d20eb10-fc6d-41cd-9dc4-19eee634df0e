@echo off
echo ========================================
echo   VERIFICATION DES EMULATEURS ANDROID
========================================
echo.

set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
set "EMULATOR_PATH=%ANDROID_HOME%\emulator\emulator.exe"
set "ADB_PATH=%ANDROID_HOME%\platform-tools\adb.exe"

echo 🔍 Vérification des outils Android...
echo.

if exist "%EMULATOR_PATH%" (
    echo ✅ Emulator trouvé : %EMULATOR_PATH%
) else (
    echo ❌ Emulator non trouvé
)

if exist "%ADB_PATH%" (
    echo ✅ ADB trouvé : %ADB_PATH%
) else (
    echo ❌ ADB non trouvé
)

echo.
echo 📱 Liste des émulateurs disponibles :
echo.

if exist "%EMULATOR_PATH%" (
    "%EMULATOR_PATH%" -list-avds
) else (
    echo ❌ Impossible de lister les émulateurs
)

echo.
echo 🚀 Pour créer un émulateur :
echo 1. Ouvrez Android Studio
echo 2. Allez dans Tools → AVD Manager
echo 3. Cliquez sur "Create Virtual Device"
echo 4. Choisissez un appareil (ex: Pixel 4)
echo 5. Téléchargez une image système Android
echo 6. Créez l'émulateur
echo.

pause
