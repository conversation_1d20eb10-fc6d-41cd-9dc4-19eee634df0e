@echo off
echo ========================================
echo   LANCEMENT AVEC ANDROID iOS EMULATOR
echo ========================================
echo.

echo 🔧 Étape 1: Vérification de l'environnement...
if not exist "node_modules" (
    echo 📦 Installation des dépendances...
    npm install
    echo.
)

echo 📱 Étape 2: Instructions pour l'émulateur...
echo.
echo DANS VS CODE :
echo 1. Appuyez sur Ctrl + Shift + P
echo 2. Tapez "Android iOS Emulator"
echo 3. Sélectionnez "Start Android Emulator"
echo 4. Choisissez un appareil Android
echo.
echo 🚀 Étape 3: Lancement de l'application...
echo Une fois l'émulateur démarré, appuyez sur une touche...
pause

echo.
echo 🔄 Démarrage d'Expo...
npx expo start --clear

echo.
echo 📋 INSTRUCTIONS FINALES :
echo 1. Attendez que l'émulateur Android soit complètement démarré
echo 2. Dan<PERSON> le terminal ci-dessus, appuyez sur 'a' pour Android
echo 3. Votre application AquaTrack se lancera sur l'émulateur
echo.
echo 🔑 Compte de test :
echo Email: <EMAIL>
echo Mot de passe: Tech123
echo.

pause
