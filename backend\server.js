const express = require('express');
const cors = require('cors');
const scannerRoutes = require('./api/scanner');
const clientsRoutes = require('./api/clients');
const consommationsRoutes = require('./api/consommations');
const facturesRoutes = require('./api/factures');

const app = express();
const PORT = process.env.PORT || 4000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3002', 'http://localhost:3001'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api', scannerRoutes);
app.use('/api', clientsRoutes);
app.use('/api', consommationsRoutes);
app.use('/api', facturesRoutes);

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'API AquaTrack - Backend Complet',
    version: '1.0.0',
    endpoints: {
      // Scanner QR
      scan: '/api/scan/:qrCode',
      qrCodes: '/api/qr-codes',
      // Clients
      clients: '/api/clients',
      clientById: '/api/clients/:id',
      clientContracts: '/api/clients/:id/contracts',
      clientFactures: '/api/clients/:id/factures',
      // Secteurs
      secteurs: '/api/secteurs',
      // Consommations
      consommations: '/api/consommations',
      clientConsommations: '/api/clients/:id/consommations',
      lastConsommation: '/api/contracts/:id/last-consommation',
      // Factures
      factures: '/api/factures',
      factureById: '/api/factures/:id',
      updateFactureStatus: '/api/factures/:id/status',
      // Test
      testDb: '/api/test-db'
    }
  });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err.stack);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Erreur serveur'
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur API AquaTrack démarré sur le port ${PORT}`);
  console.log(`📡 Endpoints disponibles:`);
  console.log(`   📋 CLIENTS:`);
  console.log(`      - GET  /api/clients          - Liste des clients`);
  console.log(`      - GET  /api/clients/:id      - Client par ID`);
  console.log(`      - GET  /api/clients/:id/contracts - Contrats d'un client`);
  console.log(`      - GET  /api/clients/:id/factures - Factures d'un client`);
  console.log(`   💧 CONSOMMATIONS:`);
  console.log(`      - GET  /api/consommations    - Liste des consommations`);
  console.log(`      - POST /api/consommations    - Créer une consommation`);
  console.log(`      - GET  /api/clients/:id/consommations - Consommations d'un client`);
  console.log(`   🧾 FACTURES:`);
  console.log(`      - GET  /api/factures         - Liste des factures`);
  console.log(`      - POST /api/factures         - Créer une facture`);
  console.log(`      - GET  /api/factures/:id     - Facture par ID`);
  console.log(`   📱 SCANNER:`);
  console.log(`      - GET  /api/scan/:qrCode     - Scanner un QR code`);
  console.log(`      - GET  /api/qr-codes         - Lister les QR codes`);
  console.log(`   🔧 AUTRES:`);
  console.log(`      - GET  /api/secteurs         - Liste des secteurs`);
  console.log(`      - GET  /api/test-db          - Tester la connexion DB`);
  console.log(`🌐 CORS autorisé pour: localhost:3000, localhost:3001, localhost:3002`);
  console.log(`🎯 Interface web: http://localhost:3002/technician-dashboard`);
});

module.exports = app;
