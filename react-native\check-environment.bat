@echo off
echo ========================================
echo   VERIFICATION DE L'ENVIRONNEMENT
echo ========================================
echo.

echo 📦 Verification de Node.js...
node --version
echo.

echo 📱 Verification de npm...
npm --version
echo.

echo 🔧 Verification d'Expo CLI...
npx expo --version
echo.

echo 📋 Verification du projet...
if exist "package.json" (
    echo ✅ package.json trouvé
) else (
    echo ❌ package.json non trouvé
)

if exist "node_modules" (
    echo ✅ node_modules trouvé
) else (
    echo ❌ node_modules non trouvé - Exécutez 'npm install'
)

if exist "App.js" (
    echo ✅ App.js trouvé
) else (
    echo ❌ App.js non trouvé
)

echo.
echo 🚀 Pour lancer l'application :
echo 1. npx expo start
echo 2. Appuyez sur 'a' pour Android
echo 3. Appuyez sur 'i' pour iOS
echo 4. <PERSON><PERSON>z le QR code avec Expo Go
echo.

pause
