@echo off
echo ========================================
echo   CONFIGURATION ANDROID SDK PATH
========================================
echo.

echo 🔍 Recherche des installations Android...
echo.

REM Chemins communs pour Android SDK
set "ANDROID_HOME_1=%LOCALAPPDATA%\Android\Sdk"
set "ANDROID_HOME_2=%PROGRAMFILES%\Android\Android Studio\sdk"
set "ANDROID_HOME_3=%PROGRAMFILES(X86)%\Android\android-sdk"
set "ANDROID_HOME_4=%USERPROFILE%\AppData\Local\Android\Sdk"

echo Vérification des chemins possibles :
echo.

if exist "%ANDROID_HOME_1%" (
    echo ✅ Trouvé : %ANDROID_HOME_1%
    set "FOUND_SDK=%ANDROID_HOME_1%"
    goto :found
)

if exist "%ANDROID_HOME_2%" (
    echo ✅ Trouvé : %ANDROID_HOME_2%
    set "FOUND_SDK=%ANDROID_HOME_2%"
    goto :found
)

if exist "%ANDROID_HOME_3%" (
    echo ✅ Trouvé : %ANDROID_HOME_3%
    set "FOUND_SDK=%ANDROID_HOME_3%"
    goto :found
)

if exist "%ANDROID_HOME_4%" (
    echo ✅ Trouvé : %ANDROID_HOME_4%
    set "FOUND_SDK=%ANDROID_HOME_4%"
    goto :found
)

echo ❌ Android SDK non trouvé dans les emplacements standards
echo.
echo 📋 Instructions manuelles :
echo 1. Ouvrez Android Studio
echo 2. Allez dans File → Settings → Appearance & Behavior → System Settings → Android SDK
echo 3. Notez le chemin "Android SDK Location"
echo 4. Ajoutez ce chemin aux variables d'environnement
goto :end

:found
echo.
echo 🎯 SDK Android trouvé : %FOUND_SDK%
echo.
echo 📝 Configuration des variables d'environnement...
setx ANDROID_HOME "%FOUND_SDK%"
setx PATH "%PATH%;%FOUND_SDK%\platform-tools;%FOUND_SDK%\tools;%FOUND_SDK%\emulator"

echo.
echo ✅ Configuration terminée !
echo 🔄 Redémarrez VS Code pour appliquer les changements
echo.

:end
pause
